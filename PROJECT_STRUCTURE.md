# Project Structure

This document outlines the directory structure and organization of the Work Smart automation system.

## Directory Structure

```
work-automations/
├── automations/           # Modular automation blocks
│   ├── wpmu-hub/         # WPMU Dev Hub related automations
│   │   └── cloneSiteViaUI.js
│   ├── wordpress/        # WordPress related automations
│   ├── monday/           # Monday.com integrations
│   └── email/            # Email automation blocks
├── lib/                  # Shared utilities and helper functions
│   ├── logger.js         # Winston logging configuration
│   └── lastpass.js       # LastPass CLI integration utilities
├── config/               # Configuration files and schemas
├── workflows/            # Workflow definitions and orchestration
│   └── createNewSite.js  # Main workflow for creating new sites
├── tests/                # Test files
│   ├── setup.js          # Jest test setup and mocks
│   └── lib/              # Tests for library functions
├── logs/                 # Log files (created at runtime)
├── main.js               # Main entry point and CLI interface
├── package.json          # Node.js dependencies and scripts
├── jest.config.js        # Jest testing configuration
├── .env.example          # Environment variables template
└── .gitignore            # Git ignore rules
```

## Key Components

### Automation Blocks (`automations/`)
- **Purpose**: Reusable automation components that perform specific tasks
- **Organization**: Grouped by service/platform (wpmu-hub, wordpress, monday, email)
- **Pattern**: Each block exports functions that can be called by workflows

### Library (`lib/`)
- **Purpose**: Shared utilities used across automation blocks and workflows
- **Key Files**:
  - `logger.js`: Centralized logging using Winston
  - `lastpass.js`: LastPass CLI integration for secure credential management

### Workflows (`workflows/`)
- **Purpose**: Orchestrate multiple automation blocks to complete complex processes
- **Pattern**: Each workflow exports an `execute()` function that takes options and returns results

### Configuration
- **Environment Variables**: Stored in `.env` file (use `.env.example` as template)
- **LastPass Integration**: Credentials stored securely in LastPass vault
- **Config Files**: Non-sensitive configuration in `config/` directory

### Testing (`tests/`)
- **Framework**: Jest with custom setup for mocking LastPass CLI
- **Structure**: Mirrors the main codebase structure
- **Mocks**: LastPass CLI calls are mocked for testing

## Usage Patterns

### Running Workflows
```bash
# Main workflow
npm start create-new-site --city "CityName" --contact-email "<EMAIL>"

# Or directly with node
node main.js create-new-site --city "CityName"
```

### Running Tests
```bash
npm test                    # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage report
```

### Installing Playwright Browsers
```bash
npm run install-browsers   # Install Playwright browser binaries
```

## Development Guidelines

1. **Modular Design**: Keep automation blocks focused on single responsibilities
2. **Error Handling**: Use try-catch blocks and proper logging
3. **Security**: Store credentials in LastPass, never in code or .env files
4. **Testing**: Write tests for all new functionality
5. **Logging**: Use the centralized logger for all output
6. **Documentation**: Update this file when adding new components

## Next Steps

1. Implement the remaining automation blocks listed in `blocks.md`
2. Complete the workflow implementations in `workflows.md`
3. Add configuration schemas in the `config/` directory
4. Expand test coverage for all components
5. Add error recovery and retry mechanisms
