/**
 * Credentials Configuration
 *
 * This file defines how to retrieve credentials from LastPass for different services.
 * No actual credentials are stored here - just the mapping to LastPass entries.
 */

const { getUsername, getPassword, getSecret } = require('../lib/lastpass');

const CREDENTIAL_CONFIG = {
	wpmudev: {
		lastpassEntry: 'Tech Team/Websites/wpmudev.com',
		fields: {
			username: 'username', // Standard LastPass field
			password: 'password', // Standard LastPass field
		},
	},

	technologyEmail: {
		lastpassEntry: 'Tech Team/Google Accounts/Technology account',
		fields: {
			email: 'username',
			password: 'password',
		},
	},

	notion: {
		lastpassEntry: 'API Keys/Notion - Impact Hub',
		fields: {
			apiKey: 'password', // API keys are typically stored in password field
		},
	},

	monday: {
		lastpassEntry: 'API Keys/Monday.com - Impact Hub',
		fields: {
			apiKey: 'password',
		},
	},
};

/**
 * Get credentials for a specific service
 * @param {string} serviceName - The service name (e.g., 'wpmudev', 'notion')
 * @returns {Promise<Object>} Object with credential fields
 */
async function getCredentials(serviceName) {
	const config = CREDENTIAL_CONFIG[serviceName];

	if (!config) {
		throw new Error(
			`No credential configuration found for service: ${serviceName}`,
		);
	}

	const credentials = {};

	// Retrieve each configured field
	for (const [fieldName, lastpassField] of Object.entries(config.fields)) {
		if (lastpassField === 'username') {
			credentials[fieldName] = await getUsername(config.lastpassEntry);
		} else if (lastpassField === 'password') {
			credentials[fieldName] = await getPassword(config.lastpassEntry);
		} else {
			credentials[fieldName] = await getSecret(
				config.lastpassEntry,
				lastpassField,
			);
		}
	}

	return credentials;
}

/**
 * Get specific credential field for a service
 * @param {string} serviceName - The service name
 * @param {string} fieldName - The field name (e.g., 'username', 'password', 'apiKey')
 * @returns {Promise<string>} The credential value
 */
async function getCredential(serviceName, fieldName) {
	const credentials = await getCredentials(serviceName);

	if (!(fieldName in credentials)) {
		throw new Error(
			`Field '${fieldName}' not found for service '${serviceName}'`,
		);
	}

	return credentials[fieldName];
}

module.exports = {
	getCredentials,
	getCredential,
	CREDENTIAL_CONFIG,
};
