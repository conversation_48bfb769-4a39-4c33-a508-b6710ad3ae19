#!/usr/bin/env node

/**
 * Work Smart - Main Entry Point
 *
 * This is the main entry point for the work automation system.
 * It handles CLI arguments and orchestrates workflow execution.
 */

require('dotenv').config();
const { program } = require('commander');
const logger = require('./lib/logger');
const { validateLastPassLogin } = require('./lib/lastpass');
const { getWpmuDevCredentials } = require('./lib/credentials');

// Import workflow modules
const createNewSiteWorkflow = require('./workflows/createNewSite');

async function main() {
	try {
		program
			.name('work-automations')
			.description('Impact Hub Work Automation System')
			.version('1.0.0');

		// Create New Site Workflow
		program
			.command('create-new-site')
			.description('Creates a new Impact Hub site from template')
			.requiredOption('--city <city>', 'The city name for the new site')
			.option(
				'--contact-email <email>',
				'The contact email address for the new site',
			)
			.action(async (options) => {
				// Validate LastPass login for production workflows
				await validateLastPassLogin();

				logger.info('Starting createNewSite workflow', {
					city: options.city,
					contactEmail: options.contactEmail,
				});
				await createNewSiteWorkflow.execute(options);
			});

		// Test commands (these bypass LastPass validation for development)
		program
			.command('test-get-wppmudev-credentials')
			.description('Test getting WPMU Dev credentials')
			.action(async () => {
				try {
					logger.info('Testing WPMU Dev credential retrieval...');
					const credentials = await getWpmuDevCredentials();
					logger.info('WPMU Dev Credentials retrieved successfully', {
						username: credentials.username,
						passwordLength: credentials.password.length,
					});
					console.log('✅ Success! Credentials retrieved from LastPass');
					console.log(`Username: ${credentials.username}`);
					console.log(
						`Password: ${'*'.repeat(credentials.password.length)} (${
							credentials.password.length
						} characters)`,
					);
				} catch (error) {
					logger.error('Failed to retrieve WPMU Dev credentials', error);
					console.log('❌ Failed to retrieve credentials');
					console.log(
						'Make sure you are logged into LastPass CLI with: <NAME_EMAIL>',
					);
				}
			});

		// Parse command line arguments
		await program.parseAsync(process.argv);
	} catch (error) {
		logger.error('Application error:', error);
		process.exit(1);
	}
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
	logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
	process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
	logger.error('Uncaught Exception:', error);
	process.exit(1);
});

if (require.main === module) {
	main();
}

module.exports = { main };
