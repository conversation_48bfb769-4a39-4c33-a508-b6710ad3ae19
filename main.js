#!/usr/bin/env node

/**
 * Work Smart - Main Entry Point
 *
 * This is the main entry point for the work automation system.
 * It handles CLI arguments and orchestrates workflow execution.
 */

require('dotenv').config();
const { program } = require('commander');
const logger = require('./lib/logger');
const { validateLastPassLogin } = require('./lib/lastpass');
const { getWpmuDevCredentials } = require('./lib/credentials');

// Import workflow modules
const createNewSiteWorkflow = require('./workflows/createNewSite');

async function main() {
	try {
		// Validate LastPass CLI is available and user is logged in
		await validateLastPassLogin();

		program
			.name('work-automations')
			.description('Impact Hub Work Automation System')
			.version('1.0.0');

		// Create New Site Workflow
		program
			.command('create-new-site')
			.description('Creates a new Impact Hub site from template')
			.requiredOption('--city <city>', 'The city name for the new site')
			.option(
				'--contact-email <email>',
				'The contact email address for the new site',
			)
			.action(async (options) => {
				logger.info('Starting createNewSite workflow', {
					city: options.city,
					contactEmail: options.contactEmail,
				});
				await createNewSiteWorkflow.execute(options);
			});

		program
			.command('test-get-wppmudev-credentials')
			.description('Test getting WPMU Dev credentials')
			.action(async () => {
				const credentials = await getWpmuDevCredentials();
				logger.info('WPMU Dev Credentials', credentials);
			});

		// Parse command line arguments
		await program.parseAsync(process.argv);
	} catch (error) {
		logger.error('Application error:', error);
		process.exit(1);
	}
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
	logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
	process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
	logger.error('Uncaught Exception:', error);
	process.exit(1);
});

if (require.main === module) {
	main();
}

module.exports = { main };
