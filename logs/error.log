{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-05T16:57:41.004Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-05T17:09:28.666Z"}
{"cmd":"lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Shared-ImpactHub/WPMU Dev Login Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Shared-ImpactHub/WPMU Dev Login\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-05T17:12:56.917Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:22.299Z"}
{"level":"error","message":"Test error message","service":"work-automations","timestamp":"2025-06-06T14:36:46.953Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:09.542Z"}
{"cmd":"lpass status","code":1,"killed":false,"level":"error","message":"Application error: Command failed: lpass status\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass status\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at Socket.<anonymous> (node:internal/child_process:457:11)\n    at Socket.emit (node:events:518:28)\n    at Pipe.<anonymous> (node:net:337:12)","stderr":"","stdout":"Not logged in.\n","timestamp":"2025-06-06T14:48:31.782Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: Tech Team/Websites/wpmudev.com Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.272Z"}
{"cmd":"lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"Tech Team/Websites/wpmudev.com\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:51:13.273Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve username from LastPass entry: undefined Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.654Z"}
{"cmd":"lpass show --field=\"username\" \"undefined\"","code":1,"killed":false,"level":"error","message":"Failed to retrieve WPMU Dev credentials Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n","service":"work-automations","signal":null,"stack":"Error: Command failed: lpass show --field=\"username\" \"undefined\"\nError: Could not find specified account(s).\n\n    at ChildProcess.exithandler (node:child_process:422:12)\n    at ChildProcess.emit (node:events:518:28)\n    at maybeClose (node:internal/child_process:1105:16)\n    at ChildProcess._handle.onexit (node:internal/child_process:305:5)","stderr":"Error: Could not find specified account(s).\n","stdout":"","timestamp":"2025-06-06T14:53:53.655Z"}
