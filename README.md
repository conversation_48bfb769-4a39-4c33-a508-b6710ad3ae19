# Work Smart

## Project Description

This project is a collection of modular automation scripts designed to streamline and automate a wide range of repetitive tasks, primarily focused on Impact Hub related work, but extensible to other areas. The core philosophy is to build reusable automation "blocks" that can be chained together to create powerful workflows.

A key security principle of this project is to leverage **`lastpass-cli`** for managing and retrieving sensitive credentials (like passwords and API keys) at runtime, minimizing the need to store them directly in `.env` files where possible.

Check out [blocks.md](blocks.md) for the list of available and planned automation blocks.
Check out [workflows.md](workflows.md) for the list of available and planned workflows.

## Prerequisites

- Node.js (v18.x or later recommended)
- npm or yarn
- Git
- Access to a terminal/command line
- **LastPass CLI (`lpass`)**: Installed and configured. You should be able to log in and access your LastPass vault via the command line.
- Playwright browsers installed (`npx playwright install`)

## Setup

1.  **Clone the repository:**

    ```bash
    git clone [your-repository-url]
    cd [repository-name]
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Install Playwright browsers:**

    ```bash
    npx playwright install
    ```

4.  **Install LastPass CLI:**
    Follow the official instructions for your operating system: [https://github.com/lastpass/lastpass-cli](https://github.com/lastpass/lastpass-cli)

    - Ensure you can log in: `lpass login your_lastpass_username`
    - Test fetching a non-sensitive item to confirm setup.

5.  **Configure Environment Variables (`.env` file):**
    - Copy the example environment file:
      ```bash
      cp .env.example .env
      ```
    - Edit the `.env` file. This file will primarily be used for:
      - **LastPass Entry Names:** Specifying the _exact names_ of the entries in your LastPass vault where secrets are stored (e.g., `LASTPASS_WPMUDEV_ENTRY_NAME="Work/WPMU Dev Login"`).
      - **Non-Sensitive Configuration:** IDs for Notion pages, Monday boards, default usernames to keep, SSH key paths (if not also in LastPass), etc.
      - **Fallback/Local Dev Secrets (Use Sparingly):** For secrets not suitable for LastPass or during initial local testing if `lastpass-cli` integration is pending.
    - **DO NOT COMMIT THE `.env` FILE TO GIT.**

## Configuration & Secrets Management

The primary method for accessing sensitive credentials (API keys, passwords) is through **`lastpass-cli`**. Scripts will be designed to fetch these secrets on-demand.

- **`lastpass-cli` Integration:** Modules requiring sensitive data will attempt to retrieve it from LastPass using predefined entry names configured in the `.env` file.
- **`.env` File:** Used for non-sensitive configurations, LastPass entry name mappings, and potentially as a fallback for local development if `lastpass-cli` is unavailable or for secrets not stored in LastPass.
- **Non-Sensitive Config Files:** General, non-secret configurations may be placed in the `config/` directory.

**Example Key Mappings/Variables in `.env`:**

```bash
# LastPass Entry Names (use actual entry names/paths in LastPass)
LASTPASS_WPMUDEV_USERNAME_FIELD="Username" # Field name within the LastPass entry
LASTPASS_WPMUDEV_PASSWORD_FIELD="Password" # Field name within the LastPass entry
LASTPASS_WPMUDEV_ENTRY="Shared-ImpactHub/WPMU Dev Login" # Example entry path/name
LASTPASS_TECHNOLOGY_EMAIL_ENTRY="Shared-ImpactHub/Tech Email Creds"
LASTPASS_NOTION_API_KEY_ENTRY="API Keys/Notion - Impact Hub"
LASTPASS_MONDAY_API_KEY_ENTRY="API Keys/Monday.com - Impact Hub"

# Non-sensitive IDs & Paths
NOTION_GREETING_DOC_PAGE_ID="your_notion_page_id_here"
MONDAY_NEW_HUBS_BOARD_ID="your_monday_board_id_here"
MONDAY_NEW_HUBS_GROUP_ID="your_monday_group_id_here"
SSH_PRIVATE_KEY_PATH="/path/to/your/ssh/private_key" # Or store the key itself in LastPass and fetch
SSH_USER="your_ssh_user"
ADMIN_USER_TO_KEEP="ulysse.coates"
```

## Available Automation Modules

_(This section will be populated as modules are developed and will detail their function and any specific configuration they need, including LastPass entry names if applicable.)_

- `wpmu-hub/cloneSiteViaUI.js`: Clones the template site using Playwright.
  - _Requires:_ LastPass entry for WPMU Dev Hub credentials.
- `wordpress/createUser.js`: Creates a new WordPress user via WP-CLI.
- `wordpress/configureElementor.js`: Configures Elementor license.
  - _Requires:_ LastPass entry for `<EMAIL>` credentials.
- ... etc.

## Running Automations

Workflows can be triggered via the command line. Ensure you are logged into `lastpass-cli` in your terminal session before running scripts that require secrets.

```bash
# Example: Ensure you are logged in to LastPass
<NAME_EMAIL>

# Then run the workflow
node main.js --workflow createNewSite --city "CityName" --contactEmail "<EMAIL>"
```

_(Specific CLI arguments for workflows will be defined as they are developed.)_

## Running Tests

```bash
npm test
```

_(Tests requiring secrets may need special handling or mocking for lastpass-cli interactions.)_

## Technology Stack

- Node.js
- Playwright (for browser UI automation)
- LastPass CLI (lpass) (for secure credential management)
- [SSH Library - ssh2] (for WP-CLI execution)
- [HTTP Client - axios] (for Notion, Monday.com APIs)
- [Logging Library - winston]
- [Testing Framework - jest]
- Dotenv (for non-secret config and LastPass entry name mapping)

## TODO / Development Plan

See TODO.md for the detailed development plan and task breakdown.

## Future Enhancements

- Broader range of automation workflows for various work tasks.
- Expose workflows via a REST API.
- Build a simple web UI for triggering automations.
- More sophisticated retry mechanisms and workflow state management.
- Dashboard for monitoring automation runs.
- GUI for building and testing workflows.
