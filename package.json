{"name": "work-automations", "version": "1.0.0", "description": "A collection of modular automation scripts designed to streamline and automate a wide range of repetitive tasks, primarily focused on Impact Hub related work, but extensible to other areas.", "main": "main.js", "directories": {"lib": "lib", "test": "tests"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "node main.js", "install-browsers": "npx playwright install"}, "keywords": ["automation", "workflow", "impact-hub", "playwright", "lastpass", "wordpress", "wpmu-dev"], "author": "Impact Hub", "license": "ISC", "dependencies": {"axios": "^1.9.0", "commander": "^14.0.0", "dotenv": "^16.5.0", "playwright": "^1.52.0", "ssh2": "^1.16.0", "winston": "^3.17.0"}, "devDependencies": {"jest": "^29.7.0"}}