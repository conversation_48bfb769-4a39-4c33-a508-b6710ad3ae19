# LastPass Configuration
# Note: LastPass entry names are configured in config/credentials.js
# This approach keeps credential mapping centralized and avoids duplication

# Non-sensitive IDs & Paths
NOTION_GREETING_DOC_PAGE_ID="your_notion_page_id_here"
MONDAY_NEW_HUBS_BOARD_ID="your_monday_board_id_here"
MONDAY_NEW_HUBS_GROUP_ID="your_monday_group_id_here"
SSH_PRIVATE_KEY_PATH="/path/to/your/ssh/private_key"
SSH_USER="your_ssh_user"
ADMIN_USER_TO_KEEP="ulysse.coates"

# Development/Testing (optional fallbacks)
# WPMU_DEV_USERNAME="fallback_username"
# WPMU_DEV_PASSWORD="fallback_password"
# NOTION_API_KEY="fallback_api_key"
# MONDAY_API_KEY="fallback_api_key"
