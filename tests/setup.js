/**
 * Jest test setup file
 */

// Load environment variables for testing
require('dotenv').config({ path: '.env.test' });

// Set test timeout
jest.setTimeout(30000);

// Mock LastPass CLI for tests
jest.mock('../lib/lastpass', () => ({
	validateLastPassLogin: jest.fn().mockResolvedValue(true),
	getSecret: jest.fn().mockResolvedValue('mock-secret'),
	getUsername: jest.fn().mockResolvedValue('mock-username'),
	getPassword: jest.fn().mockImplementation((entryName) => {
		// Return different values based on entry name
		if (entryName.includes('API Keys')) {
			return Promise.resolve('mock-api-key');
		}
		return Promise.resolve('mock-password');
	}),
	getApiKey: jest.fn().mockResolvedValue('mock-api-key'),
}));

// Global test utilities
global.testUtils = {
	// Add common test utilities here
};
