/**
 * Tests for credentials configuration
 */

const { getCredentials, getCredential, CREDENTIAL_CONFIG } = require('../../config/credentials');

// Mock the lastpass module
jest.mock('../../lib/lastpass');

describe('Credentials Configuration', () => {
  test('should have valid credential configuration', () => {
    expect(CREDENTIAL_CONFIG).toBeDefined();
    expect(CREDENTIAL_CONFIG.wpmudev).toBeDefined();
    expect(CREDENTIAL_CONFIG.wpmudev.lastpassEntry).toBe('Shared-ImpactHub/WPMU Dev Login');
    expect(CREDENTIAL_CONFIG.wpmudev.fields.username).toBe('username');
    expect(CREDENTIAL_CONFIG.wpmudev.fields.password).toBe('password');
  });

  test('should get credentials for wpmudev service', async () => {
    const credentials = await getCredentials('wpmudev');
    
    expect(credentials).toHaveProperty('username');
    expect(credentials).toHaveProperty('password');
    expect(credentials.username).toBe('mock-username');
    expect(credentials.password).toBe('mock-password');
  });

  test('should get specific credential field', async () => {
    const username = await getCredential('wpmudev', 'username');
    const password = await getCredential('wpmudev', 'password');
    
    expect(username).toBe('mock-username');
    expect(password).toBe('mock-password');
  });

  test('should throw error for unknown service', async () => {
    await expect(getCredentials('unknown-service')).rejects.toThrow(
      'No credential configuration found for service: unknown-service'
    );
  });

  test('should throw error for unknown field', async () => {
    await expect(getCredential('wpmudev', 'unknown-field')).rejects.toThrow(
      "Field 'unknown-field' not found for service 'wpmudev'"
    );
  });

  test('should get API key credentials', async () => {
    const notionCreds = await getCredentials('notion');
    const mondayCreds = await getCredentials('monday');
    
    expect(notionCreds).toHaveProperty('apiKey');
    expect(mondayCreds).toHaveProperty('apiKey');
    expect(notionCreds.apiKey).toBe('mock-api-key');
    expect(mondayCreds.apiKey).toBe('mock-api-key');
  });
});
