/**
 * Tests for logger utility
 */

const logger = require('../../lib/logger');

describe('Logger', () => {
	test('should be defined', () => {
		expect(logger).toBeDefined();
	});

	test('should have required methods', () => {
		expect(typeof logger.info).toBe('function');
		expect(typeof logger.error).toBe('function');
		expect(typeof logger.warn).toBe('function');
		expect(typeof logger.debug).toBe('function');
	});

	test('should log info messages', () => {
		// Test that logger methods can be called without throwing
		expect(() => {
			logger.info('Test info message');
			logger.error('Test error message');
			logger.warn('Test warning message');
			logger.debug('Test debug message');
		}).not.toThrow();
	});
});
