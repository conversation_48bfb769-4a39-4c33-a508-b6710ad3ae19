/**
 * Credential Helper Utilities
 * 
 * Convenience functions for common credential operations
 */

const { getCredentials, getCredential } = require('../config/credentials');

/**
 * Get WPMU Dev credentials
 * @returns {Promise<{username: string, password: string}>}
 */
async function getWpmuDevCredentials() {
  return getCredentials('wpmudev');
}

/**
 * Get technology email credentials
 * @returns {Promise<{email: string, password: string}>}
 */
async function getTechnologyEmailCredentials() {
  return getCredentials('technologyEmail');
}

/**
 * Get Notion API key
 * @returns {Promise<string>}
 */
async function getNotionApiKey() {
  return getCredential('notion', 'apiKey');
}

/**
 * Get Monday.com API key
 * @returns {Promise<string>}
 */
async function getMondayApiKey() {
  return getCredential('monday', 'apiKey');
}

module.exports = {
  getWpmuDevCredentials,
  getTechnologyEmailCredentials,
  getNotionApiKey,
  getMondayApiKey,
  // Re-export the core functions for flexibility
  getCredentials,
  getCredential
};
