/**
 * LastPass CLI integration utilities
 */

const { exec } = require('child_process');
const { promisify } = require('util');
const logger = require('./logger');

const execAsync = promisify(exec);

/**
 * Check if LastPass CLI is installed and user is logged in
 */
async function validateLastPassLogin() {
  try {
    // Check if lpass is installed
    await execAsync('which lpass');
    
    // Check if user is logged in
    const { stdout } = await execAsync('lpass status');
    
    if (stdout.includes('Not logged in')) {
      throw new Error('Please log in to LastPass CLI using: lpass login <username>');
    }
    
    logger.info('LastPass CLI validation successful');
    return true;
  } catch (error) {
    if (error.code === 1 && error.message.includes('which lpass')) {
      throw new Error('LastPass CLI is not installed. Please install it from: https://github.com/lastpass/lastpass-cli');
    }
    throw error;
  }
}

/**
 * Retrieve a secret from LastPass
 * @param {string} entryName - The name/path of the LastPass entry
 * @param {string} field - The field to retrieve (default: 'password')
 * @returns {Promise<string>} The secret value
 */
async function getSecret(entryName, field = 'password') {
  try {
    const command = field === 'password' 
      ? `lpass show --password "${entryName}"`
      : `lpass show --field="${field}" "${entryName}"`;
    
    const { stdout } = await execAsync(command);
    const secret = stdout.trim();
    
    if (!secret) {
      throw new Error(`No ${field} found for entry: ${entryName}`);
    }
    
    logger.debug(`Retrieved ${field} from LastPass entry: ${entryName}`);
    return secret;
  } catch (error) {
    logger.error(`Failed to retrieve ${field} from LastPass entry: ${entryName}`, error);
    throw error;
  }
}

/**
 * Get username from LastPass entry
 * @param {string} entryName - The name/path of the LastPass entry
 * @returns {Promise<string>} The username
 */
async function getUsername(entryName) {
  return getSecret(entryName, 'username');
}

/**
 * Get password from LastPass entry
 * @param {string} entryName - The name/path of the LastPass entry
 * @returns {Promise<string>} The password
 */
async function getPassword(entryName) {
  return getSecret(entryName, 'password');
}

/**
 * Get API key from LastPass entry
 * @param {string} entryName - The name/path of the LastPass entry
 * @returns {Promise<string>} The API key
 */
async function getApiKey(entryName) {
  return getSecret(entryName, 'password'); // API keys are typically stored in password field
}

module.exports = {
  validateLastPassLogin,
  getSecret,
  getUsername,
  getPassword,
  getApiKey
};
